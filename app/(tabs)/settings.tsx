import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Settings as SettingsIcon,
  Timer,
  Bell,
  Palette,
  Info,
  Trash2,
  Download,
  Upload,
  User,
  LogOut,
  Cloud,
  CloudOff,
} from 'lucide-react-native';
import IsotopeLogo from '@/components/IsotopeLogo';
import { useTimer } from '@/hooks/useTimer';
import { useAuth } from '@/contexts/AuthContext';
import { PomodoroSettings } from '@/types/app';

export default function SettingsScreen() {
  const { pomodoroSettings, savePomodoroSettings } = useTimer();
  const { user, signOut } = useAuth();
  const [localSettings, setLocalSettings] = useState<PomodoroSettings>(pomodoroSettings);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);

  const handleSaveSettings = () => {
    savePomodoroSettings(localSettings);
    Alert.alert('Success', 'Settings saved successfully!');
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            const { error } = await signOut();
            if (error) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleResetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            const defaultSettings: PomodoroSettings = {
              workDuration: 25,
              shortBreakDuration: 5,
              longBreakDuration: 15,
              sessionsUntilLongBreak: 4,
            };
            setLocalSettings(defaultSettings);
            savePomodoroSettings(defaultSettings);
          },
        },
      ]
    );
  };

  const handleClearData = () => {
    Alert.alert(
      'Clear All Data',
      'This will permanently delete all your timer sessions, subjects, and goals. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear Data',
          style: 'destructive',
          onPress: async () => {
            try {
              // Clear all data using storage service
              const { storageService } = require('@/services/storageService');
              await storageService.multiRemove([
                'isotope_timer_sessions',
                'isotope_subjects',
                'isotope_goals',
                'isotope_pomodoro_settings',
                'distraction_blocking_data'
              ]);
              Alert.alert('Success', 'All data has been cleared.');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear data.');
            }
          },
        },
      ]
    );
  };

  const exportData = () => {
    if (typeof window !== 'undefined' && window.localStorage) {
      const data = {
        sessions: localStorage.getItem('isotope_timer_sessions'),
        subjects: localStorage.getItem('isotope_subjects'),
        goals: localStorage.getItem('isotope_goals'),
        settings: localStorage.getItem('isotope_pomodoro_settings'),
        exportDate: new Date().toISOString(),
      };
      
      const dataStr = JSON.stringify(data, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `isotope-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      Alert.alert('Success', 'Data exported successfully!');
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <View>
            <IsotopeLogo size="medium" />
            <Text style={styles.subtitle}>Customize your experience</Text>
          </View>
        </View>
      </LinearGradient>

      {/* Account Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <User size={20} color="#6366F1" />
          <Text style={styles.sectionTitle}>Account</Text>
        </View>

        <View style={styles.settingsCard}>
          <View style={styles.accountInfo}>
            <View style={styles.userAvatar}>
              <Text style={styles.userInitial}>
                {user?.email?.charAt(0).toUpperCase() || 'U'}
              </Text>
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userEmail}>{user?.email}</Text>
              <View style={styles.syncStatus}>
                <Cloud size={16} color="#10B981" />
                <Text style={styles.syncText}>Synced to cloud</Text>
              </View>
            </View>
          </View>

          <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
            <LogOut size={20} color="#EF4444" />
            <Text style={styles.signOutText}>Sign Out</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Pomodoro Settings */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Timer size={20} color="#6366F1" />
          <Text style={styles.sectionTitle}>Pomodoro Timer</Text>
        </View>
        
        <View style={styles.settingsCard}>
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Work Duration (minutes)</Text>
            <TextInput
              style={styles.settingInput}
              value={localSettings.workDuration.toString()}
              onChangeText={(text) => 
                setLocalSettings(prev => ({ ...prev, workDuration: parseInt(text) || 25 }))
              }
              keyboardType="number-pad"
              placeholder="25"
            />
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Short Break (minutes)</Text>
            <TextInput
              style={styles.settingInput}
              value={localSettings.shortBreakDuration.toString()}
              onChangeText={(text) => 
                setLocalSettings(prev => ({ ...prev, shortBreakDuration: parseInt(text) || 5 }))
              }
              keyboardType="number-pad"
              placeholder="5"
            />
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Long Break (minutes)</Text>
            <TextInput
              style={styles.settingInput}
              value={localSettings.longBreakDuration.toString()}
              onChangeText={(text) => 
                setLocalSettings(prev => ({ ...prev, longBreakDuration: parseInt(text) || 15 }))
              }
              keyboardType="number-pad"
              placeholder="15"
            />
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Sessions until Long Break</Text>
            <TextInput
              style={styles.settingInput}
              value={localSettings.sessionsUntilLongBreak.toString()}
              onChangeText={(text) => 
                setLocalSettings(prev => ({ ...prev, sessionsUntilLongBreak: parseInt(text) || 4 }))
              }
              keyboardType="number-pad"
              placeholder="4"
            />
          </View>
          
          <TouchableOpacity style={styles.saveButton} onPress={handleSaveSettings}>
            <LinearGradient
              colors={['#6366F1', '#8B5CF6']}
              style={styles.saveGradient}
            >
              <Text style={styles.saveText}>Save Settings</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>

      {/* Notifications */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Bell size={20} color="#10B981" />
          <Text style={styles.sectionTitle}>Notifications</Text>
        </View>
        
        <View style={styles.settingsCard}>
          <View style={styles.switchItem}>
            <View style={styles.switchInfo}>
              <Text style={styles.settingLabel}>Enable Notifications</Text>
              <Text style={styles.settingDescription}>
                Get notified when timer sessions complete
              </Text>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: '#E5E7EB', true: '#6366F1' }}
              thumbColor={notificationsEnabled ? '#FFFFFF' : '#F3F4F6'}
            />
          </View>
          
          <View style={styles.switchItem}>
            <View style={styles.switchInfo}>
              <Text style={styles.settingLabel}>Sound Alerts</Text>
              <Text style={styles.settingDescription}>
                Play sound when timer completes
              </Text>
            </View>
            <Switch
              value={soundEnabled}
              onValueChange={setSoundEnabled}
              trackColor={{ false: '#E5E7EB', true: '#6366F1' }}
              thumbColor={soundEnabled ? '#FFFFFF' : '#F3F4F6'}
            />
          </View>
        </View>
      </View>

      {/* Data Management */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <SettingsIcon size={20} color="#F59E0B" />
          <Text style={styles.sectionTitle}>Data Management</Text>
        </View>
        
        <View style={styles.settingsCard}>
          <TouchableOpacity style={styles.actionButton} onPress={exportData}>
            <Download size={20} color="#6366F1" />
            <View style={styles.actionInfo}>
              <Text style={styles.actionLabel}>Export Data</Text>
              <Text style={styles.actionDescription}>
                Download your data as a backup file
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleResetSettings}>
            <Upload size={20} color="#F59E0B" />
            <View style={styles.actionInfo}>
              <Text style={styles.actionLabel}>Reset Settings</Text>
              <Text style={styles.actionDescription}>
                Restore default timer settings
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleClearData}>
            <Trash2 size={20} color="#EF4444" />
            <View style={styles.actionInfo}>
              <Text style={styles.actionLabel}>Clear All Data</Text>
              <Text style={styles.actionDescription}>
                Permanently delete all your data
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* About */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Info size={20} color="#8B5CF6" />
          <Text style={styles.sectionTitle}>About</Text>
        </View>
        
        <View style={styles.settingsCard}>
          <View style={styles.aboutContent}>
            <IsotopeLogo size="large" />
            <Text style={styles.appName}>IsotopeAI Time Management</Text>
            <Text style={styles.version}>Version 1.0.0</Text>
            <Text style={styles.description}>
              A powerful time management application designed to help you maximize 
              your productivity with advanced timer features, goal tracking, and 
              detailed analytics.
            </Text>
          </View>
        </View>
      </View>

      {/* Tips */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>💡 Tips</Text>
        
        <View style={styles.tipCard}>
          <Text style={styles.tipTitle}>Pomodoro Technique</Text>
          <Text style={styles.tipText}>
            The traditional Pomodoro technique uses 25-minute work sessions with 5-minute breaks. 
            After 4 sessions, take a longer 15-30 minute break.
          </Text>
        </View>
        
        <View style={styles.tipCard}>
          <Text style={styles.tipTitle}>Customize Your Settings</Text>
          <Text style={styles.tipText}>
            Adjust timer durations based on your attention span and work style. 
            Some people work better with longer or shorter intervals.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  section: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  settingsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    marginBottom: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
    marginBottom: 8,
  },
  settingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  settingInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
  },
  switchItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchInfo: {
    flex: 1,
    marginRight: 16,
  },
  saveButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: 8,
  },
  saveGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 12,
  },
  actionInfo: {
    flex: 1,
  },
  actionLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
  },
  actionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  aboutContent: {
    alignItems: 'center',
    gap: 12,
  },
  appName: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    textAlign: 'center',
  },
  version: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  description: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    marginTop: 8,
  },
  tipCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  // Account styles
  accountInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#6366F1',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  userInitial: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  userDetails: {
    flex: 1,
  },
  userEmail: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  syncStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  syncText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#10B981',
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FEF2F2',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#FECACA',
    gap: 8,
  },
  signOutText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#EF4444',
  },
});